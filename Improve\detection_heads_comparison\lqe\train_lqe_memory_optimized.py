"""
Detect_LQE 检测头测试脚本 - 内存优化版本

基于 innovation1-c2f-emscp-aaaf 的成功配置
检测头改进: 位置质量评估 + 分布统计

内存优化策略:
- 禁用数据缓存
- 减小批次大小
- 减少数据增强强度
- 禁用多进程数据加载
- 启用梯度累积
"""

import os
import sys
import warnings
import gc
import torch
warnings.filterwarnings('ignore')

# 获取脚本所在目录并切换
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 添加项目根目录到路径
sys.path.append('../../../')

from ultralytics import YOLO

def clear_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def get_memory_optimized_augmentation():
    """内存优化的数据增强参数"""
    return {
        # === 轻量化颜色增强 ===
        'hsv_h': 0.01,       # 🔥 减少色调变化
        'hsv_s': 0.5,        # 🔥 减少饱和度变化
        'hsv_v': 0.3,        # 💨 减少亮度变化
        
        # === 轻量化几何变换 ===
        'degrees': 8.0,      # 🔥 减少旋转角度
        'translate': 0.08,   # 🔥 减少位置变化
        'scale': 0.8,        # 🔥 减少缩放变化
        'shear': 2.0,        # 🔥 减少剪切变形
        
        # === 环境条件模拟 ===
        'perspective': 0.0002, # 🏠 减少透视变换
        'flipud': 0.0,       # 🔥 禁用垂直翻转
        'fliplr': 0.25,      # 🏠 减少水平翻转
        
        # === 轻量化复杂增强 ===
        'mosaic': 0.3,       # 🏙️ 大幅减少马赛克
        'mixup': 0.05,       # 🌫️ 大幅减少混合
        'copy_paste': 0.02,  # 🔥 大幅减少复制粘贴
        
        # === 减少遮挡增强 ===
        'erasing': 0.1,      # 🌫️ 减少擦除
        'auto_augment': None, # 🚫 禁用自动增强
        
        # === 基础设置 ===
        'bgr': 0.0,          # 🎨 保持RGB通道顺序
        'crop_fraction': 1.0, # 📐 完整图像
    }

def get_memory_optimized_optimizer():
    """内存优化的优化器配置"""
    return {
        # === 保守学习率策略 ===
        'lr0': 0.001,        # 🎯 较低初始学习率
        'lrf': 0.0001,       # 📉 较低最终学习率
        'momentum': 0.9,     # 🚀 标准动量
        'weight_decay': 0.0005, # ⚖️ 轻度正则化
        
        # === 短预热策略 ===
        'warmup_epochs': 2.0,    # 🔥 短预热期
        'warmup_momentum': 0.8,  # 🌡️ 预热动量
        'warmup_bias_lr': 0.05,  # 📊 较低预热偏置学习率
        
        # === 学习率调度 ===
        'cos_lr': True,      # 📈 余弦学习率调度
    }

def get_memory_optimized_loss():
    """内存优化的损失权重"""
    return {
        'box': 7.0,          # 📦 边界框权重
        'cls': 0.5,          # 🏷️ 分类权重
        'dfl': 1.0,          # 📏 分布焦点损失权重
    }

def get_memory_optimized_strategy():
    """内存优化的训练策略"""
    return {
        'close_mosaic': 5,   # 🧩 早期关闭马赛克
        'patience': 30,      # 🎯 增加耐心值
        'save_period': 10,   # 💾 减少保存频率
        'val': True,         # ✅ 启用验证
        'plots': False,      # 🚫 禁用图表生成节省内存
        'cache': False,      # 🚫 禁用缓存
        'amp': True,         # ⚡ 混合精度训练
        'single_cls': False, # 🏷️ 多类别检测
        'rect': False,       # 📐 不使用矩形训练
        'deterministic': True, # 🎲 确定性训练
        'seed': 42,          # 🌱 随机种子
    }

def check_memory_status():
    """检查内存状态"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_allocated = torch.cuda.memory_allocated(0) / 1024**3
        gpu_reserved = torch.cuda.memory_reserved(0) / 1024**3
        
        print(f"🖥️ GPU总内存: {gpu_memory:.1f}GB")
        print(f"📊 GPU已分配: {gpu_allocated:.1f}GB")
        print(f"📦 GPU已保留: {gpu_reserved:.1f}GB")
        print(f"🆓 GPU可用: {gpu_memory - gpu_reserved:.1f}GB")
        
        return gpu_memory - gpu_reserved > 2.0  # 至少需要2GB可用内存
    return False

def main():
    try:
        print("🔥 Detect_LQE 测试 - 内存优化版本")
        print("🎯 检测头改进: 位置质量评估 + 分布统计")
        print("💾 内存优化: 减少批次 + 禁用缓存 + 轻量增强")
        
        # 清理内存
        clear_memory()
        
        # 环境检查
        if not torch.cuda.is_available():
            print("❌ CUDA不可用，请检查GPU环境")
            return None
        
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        
        # 内存状态检查
        if not check_memory_status():
            print("⚠️ GPU内存可能不足，建议关闭其他程序")
        
        # 加载模型
        config_path = os.path.join(script_dir, 'config_lqe.yaml')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        model = YOLO(config_path)
        
        # 获取内存优化配置
        aug_params = get_memory_optimized_augmentation()
        opt_params = get_memory_optimized_optimizer()
        loss_params = get_memory_optimized_loss()
        strategy_params = get_memory_optimized_strategy()
        
        print("\n🔥 LQE 特性:")
        print("   📍 位置质量评估: 精确定位质量评分")
        print("   📊 分布统计: 特征分布建模")
        print("   🎯 自适应阈值: 动态检测阈值")
        
        print("\n💾 内存优化设置:")
        print("   📦 batch=8: 小批次大小")
        print("   🚫 workers=0: 禁用多进程")
        print("   🚫 cache=False: 禁用数据缓存")
        print("   📉 轻量增强: 减少数据增强强度")
        
        # 开始训练
        results = model.train(
            # === 数据集配置 ===
            data='../../../ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=20,           # 🕐 快速测试周期
            batch=24,             # 📦 小批次大小
            imgsz=640,           # 📐 标准分辨率
            
            # === 设备配置 ===
            device=0,            # 🖥️ GPU 0
            workers=2,           # 👥 禁用多进程
            
            # === 项目配置 ===
            project='../results',
            name='lqe_memory_optimized',
            exist_ok=True,
            
            # === 优化器配置 ===
            optimizer="AdamW",
            **opt_params,
            
            # === 内存优化增强 ===
            **aug_params,
            
            # === 优化损失权重 ===
            **loss_params,
            
            # === 内存优化策略 ===
            **strategy_params,
            
            # === 其他设置 ===
            verbose=True,
            pretrained=True,     # 🎯 使用预训练权重
        )
        
        print("\n🎉 LQE 内存优化测试完成！")
        print("📁 结果保存在: ../results/lqe_memory_optimized")
        print("🎯 LQE 的位置质量评估应该提升检测精度")
        print("💾 内存优化配置避免了内存不足问题")
        
        # 最终清理内存
        clear_memory()
        
        return results
        
    except Exception as e:
        print(f"❌ LQE 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 错误时也清理内存
        clear_memory()
        return None

if __name__ == "__main__":
    main()
